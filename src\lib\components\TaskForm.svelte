<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import RecurrenceEditor from '$lib/client/RecurrenceEditor.svelte';
  import { slide } from 'svelte/transition';

  // Props for mode and form fields
  export let mode: 'create' | 'edit' = 'create';
  export let title = '';
  export let notes = '';
  export let priority = 1;
  export let categoryId = '';
  export let dueDate = '';
  export let dueTime = '';
  export let hasDueDate = false;
  export let subtasks: string[] = [''];
  export let loading = false;
  export let error = '';
  export let success = '';
  export let showCategoryDropdown = false;
  export let hasRecurrence = false;
  export let recurrenceRule: any = null;
  export let categories: Array<{id: string, name: string, color: string}> = [];

  const dispatch = createEventDispatcher();

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#64748b' },
    { value: 1, label: 'Normal', color: '#059669' },
    { value: 2, label: 'High', color: '#dc2626' }
  ];

  function addSubtask() {
    subtasks = [...subtasks, ''];
  }

  function removeSubtask(index: number) {
    subtasks = subtasks.filter((_, i) => i !== index);
  }

  function handleRecurrenceChange(event: CustomEvent) {
    recurrenceRule = event.detail;
  }

  function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    // Combine date and time if both are provided
    let combinedDueDate = null;
    if (hasDueDate && dueDate) {
      if (dueTime) {
        combinedDueDate = new Date(`${dueDate}T${dueTime}`);
      } else {
        combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
      }
    }

    // Filter out empty subtasks
    const validSubtasks = subtasks.filter(s => s.trim() !== '');

    const formData = {
      title: title.trim(),
      notes: notes.trim() || null,
      priority,
      categoryId: categoryId || null,
      dueDate: combinedDueDate?.toISOString() || null,
      subtasks: validSubtasks,
      recurrenceRule: hasRecurrence ? recurrenceRule : null
    };

    dispatch('submit', formData);
  }

  function handleCancel() {
    dispatch('cancel');
  }
</script>

<div class="form-container">
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <!-- Essential Information Section -->
    <div class="form-section">
      <div class="section-header">
        <h3 class="section-title">Essential Information</h3>
        <p class="section-subtitle">The basics to get your task started</p>
      </div>

      <div class="form-group">
        <label for="title">Task Title *</label>
        <div class="title-input-row">
          <!-- Desktop: input -->
          <input
            id="title"
            type="text"
            bind:value={title}
            placeholder="What needs to be done?"
            required
            disabled={loading}
            class="primary-input hidden md:block"
          />
          <!-- Mobile: textarea -->
          <textarea
            id="title-mobile"
            bind:value={title}
            placeholder="What needs to be done?"
            required
            disabled={loading}
            class="primary-input md:hidden"
            rows="2"
          ></textarea>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Priority Level</label>
          <div class="priority-selector">
            {#each priorityOptions as option}
              <button
                type="button"
                class="priority-btn"
                class:selected={priority === option.value}
                style="--priority-color: {option.color}"
                on:click={() => priority = option.value}
                disabled={loading}
              >
                <div class="priority-indicator"></div>
                {option.label}
              </button>
            {/each}
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="due-date-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              bind:checked={hasDueDate}
              disabled={loading}
              class="toggle-checkbox"
            />
            <span class="toggle-text">Set Due Date</span>
          </label>
        </div>

        {#if hasDueDate}
          <div class="due-date-inputs" transition:slide>
            <div class="date-input-group">
              <label class="input-label">Date</label>
              <div class="custom-date-input">
                <input
                  type="date"
                  bind:value={dueDate}
                  disabled={loading}
                  class="date-input"
                />
                <div class="input-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </div>
              </div>
            </div>
            <div class="time-input-group">
              <label class="input-label">Time (optional)</label>
              <div class="custom-time-input">
                <input
                  type="time"
                  bind:value={dueTime}
                  disabled={loading}
                  class="time-input"
                  placeholder="09:00"
                />
                <div class="input-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <div class="form-group">
        <label>Subtasks (Optional)</label>
        <div class="subtasks-container">
          {#each subtasks as subtask, index}
            <div class="subtask-row">
              <!-- Desktop: input -->
              <input
                type="text"
                bind:value={subtasks[index]}
                placeholder="Enter subtask"
                disabled={loading}
                class="hidden md:block"
              />
              <!-- Mobile: textarea -->
              <textarea
                bind:value={subtasks[index]}
                placeholder="Enter subtask"
                disabled={loading}
                class="md:hidden subtask-textarea"
                rows="1"
              ></textarea>
              {#if subtasks.length > 1}
                <button
                  type="button"
                  class="remove-subtask-btn"
                  on:click={() => removeSubtask(index)}
                  disabled={loading}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              {/if}
            </div>
          {/each}
          <button
            type="button"
            class="add-subtask-btn"
            on:click={addSubtask}
            disabled={loading}
          >
            + Add Subtask
          </button>
        </div>
      </div>
    </div>

    <!-- Additional Details Section -->
    <div class="form-section collapsible">
      <div class="section-header">
        <h3 class="section-title">Additional Details</h3>
        <p class="section-subtitle">Optional information to organize your task</p>
      </div>

      <div class="form-group">
        <label>Category</label>
        <div class="category-dropdown-container">
          <button
            type="button"
            class="category-dropdown-trigger"
            on:click={() => showCategoryDropdown = !showCategoryDropdown}
            disabled={loading}
          >
            <div class="selected-category">
              {#if categoryId}
                {@const selectedCategory = categories.find(c => c.id === categoryId)}
                <div class="category-color" style="background: {selectedCategory?.color};"></div>
                <span>{selectedCategory?.name}</span>
              {:else}
                <div class="category-color" style="background: #e5e7eb;"></div>
                <span>No category</span>
              {/if}
            </div>
            <svg class="dropdown-arrow" class:rotated={showCategoryDropdown} width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m6 8 4 4 4-4"/>
            </svg>
          </button>

          {#if showCategoryDropdown}
            <div class="category-dropdown-menu" transition:slide>
              <button
                type="button"
                class="category-option"
                class:selected={!categoryId}
                on:click={() => { categoryId = ''; showCategoryDropdown = false; }}
                disabled={loading}
              >
                <div class="category-color" style="background: #e5e7eb;"></div>
                <span>No category</span>
              </button>
              {#each categories as category}
                <button
                  type="button"
                  class="category-option"
                  class:selected={categoryId === category.id}
                  on:click={() => { categoryId = category.id; showCategoryDropdown = false; }}
                  disabled={loading}
                >
                  <div class="category-color" style="background: {category.color};"></div>
                  <span>{category.name}</span>
                </button>
              {/each}
            </div>
          {/if}
        </div>
      </div>

      <div class="form-group">
        <label for="notes">Notes</label>
        <textarea
          id="notes"
          bind:value={notes}
          placeholder="Add any additional notes or details..."
          rows="3"
          disabled={loading}
        ></textarea>
      </div>
    </div>

    <!-- Advanced Settings Section -->
    <div class="form-section advanced">
      <!-- Desktop Header -->
      <div class="section-header hidden md:block">
        <h3 class="section-title">Advanced Settings</h3>
        <p class="section-subtitle">Set up recurring tasks and automation</p>
      </div>
      <!-- Mobile: No header, just content -->

      <div class="form-group">
        <div class="recurrence-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              bind:checked={hasRecurrence}
              disabled={loading}
              class="toggle-checkbox"
            />
            <span class="toggle-text">Make this a recurring task</span>
          </label>
        </div>

        {#if hasRecurrence}
          <div transition:slide>
            <RecurrenceEditor rule={recurrenceRule} on:change={handleRecurrenceChange} />
          </div>
        {/if}
      </div>
    </div>

    <div class="form-actions">
      <!-- Desktop Cancel Button -->
      <button type="button" class="btn-secondary hidden md:block" on:click={handleCancel} disabled={loading}>
        Cancel
      </button>
      <button type="submit" class="btn-primary" disabled={loading}>
        {#if loading}
          <span class="loading-spinner"></span>
          {mode === 'create' ? 'Creating...' : 'Updating...'}
        {:else}
          {mode === 'create' ? 'Create Task' : 'Update Task'}
        {/if}
      </button>
    </div>
  </form>
</div>

<style>
  .form-container {
    /* max-width is now on page-container */
  }

  .task-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    padding: 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  /* Form sections */
  .form-section {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .form-section:last-of-type {
    border-bottom: none;
  }

  .form-section.collapsible {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  }

  .form-section.advanced {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95), rgba(241, 245, 249, 0.9));
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
  }

  .form-section.advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #0ea5e9);
    border-radius: 16px 16px 0 0;
  }

  .form-section.advanced .section-header {
    position: relative;
    z-index: 1;
    padding-top: 0.75rem;
  }

  .form-section.advanced .section-title {
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .form-section.advanced .section-subtitle {
    color: #6b7280;
    font-weight: 500;
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.125rem;
  }

  .section-subtitle {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, textarea, select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
  }

  .title-input-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .title-input-row .primary-input {
    flex: 1;
    border: 2px solid #d1d5db !important;
    font-size: 1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.75rem;
    border-radius: 10px;
  }

  .primary-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.08);
  }

  /* Toggle styles */
  .toggle-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .toggle-checkbox {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #3b82f6;
  }

  .toggle-text {
    color: #374151;
  }

  /* Due date inputs */
  .due-date-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin-top: 0.75rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .date-input-group, .time-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .input-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .custom-date-input, .custom-time-input {
    position: relative;
    display: flex;
    align-items: center;
  }

  .date-input, .time-input {
    width: 100%;
    padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    font-size: 0.875rem;
    background: white;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #374151;
  }

  .date-input:focus, .time-input:focus {
    outline: none;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
    transform: translateY(-1px) !important;
    background: rgba(255, 255, 255, 1) !important;
  }

  .date-input:hover, .time-input:hover {
    border-color: #9ca3af;
    transform: translateY(-1px);
  }

  .input-icon {
    position: absolute;
    right: 0.875rem;
    color: #6b7280;
    pointer-events: none;
    transition: color 0.2s;
  }

  .custom-date-input:focus-within .input-icon,
  .custom-time-input:focus-within .input-icon {
    color: #3b82f6;
  }

  input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
  }

  input:disabled, textarea:disabled, select:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  /* Form actions */
  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
    margin-bottom: 2rem;
    border-top: 1px solid #e9ecef;
  }

  .btn-primary, .btn-secondary {
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(249, 250, 251, 0.9);
    transform: translateY(-1px);
  }

  .btn-primary:disabled, .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  textarea {
    resize: vertical;
    min-height: 80px;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .form-container {
      margin: 0.5rem;
    }

    .task-form {
      border-radius: 16px;
      padding: 1rem;
    }

    .form-section {
      padding: 1rem;
      margin-bottom: 0.75rem;
    }

    .form-section.collapsible {
      background: transparent;
      border: none;
      box-shadow: none;
      padding: 1rem;
      margin-bottom: 0.25rem;
    }

    .form-section.advanced {
      padding: 1rem;
      background: transparent;
      border: none;
      box-shadow: none;
      margin-bottom: 0;
      margin-top: 0 !important;
      padding-top: 0 !important;
    }

    .recurrence-toggle {
      margin-bottom: 1rem;
      margin-top: 0.75rem;
    }

    .toggle-label {
      font-size: 0.85rem;
      margin-bottom: 0.75rem;
      font-weight: 500;
      color: #374151;
    }

    .toggle-text {
      font-size: 0.8rem;
    }

    .toggle-checkbox {
      width: 18px;
      height: 18px;
    }

    .section-title {
      font-size: 0.85rem;
      margin-bottom: 0.25rem;
    }

    .section-subtitle {
      font-size: 0.75rem;
      margin-bottom: 0.5rem;
    }

    label {
      font-size: 0.85rem;
      margin-bottom: 0.4rem;
    }

    input, textarea, select {
      padding: 0.75rem;
      font-size: 0.9rem;
      border-radius: 8px;
    }

    select {
      padding: 0.3rem !important;
      font-size: 0.7rem !important;
      height: 32px !important;
    }

    .title-input-row .primary-input {
      padding: 0.75rem;
      font-size: 0.9rem;
      border-radius: 8px;
      min-height: 48px;
      resize: vertical;
    }

    .form-row {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .due-date-inputs {
      grid-template-columns: 1fr;
      gap: 0.75rem;
      padding: 1rem;
    }

    .form-actions {
      flex-direction: column;
      gap: 0.75rem;
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .btn-primary {
      width: 100%;
      justify-content: center;
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }

    .btn-secondary {
      display: none !important;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-section.advanced .form-group:first-child {
      margin-top: 0.75rem !important;
      padding-top: 0 !important;
    }

    .priority-selector {
      grid-template-columns: repeat(3, 1fr);
      gap: 0.4rem;
    }

    .priority-btn {
      padding: 0.25rem 0.05rem !important;
      font-size: 0.65rem !important;
      border-radius: 6px !important;
      gap: 0.1rem !important;
      min-height: 20px !important;
      flex: 1 !important;
    }

    .priority-indicator {
      width: 4px;
      height: 4px;
    }
  }

  /* Message styles */
  .error-message {
    background: linear-gradient(135deg, #fef2f2, #fed7d7);
    color: #dc2626;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 600;
    border: 1px solid #fecaca;
  }

  .success-message {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #16a34a;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 600;
    border: 1px solid #bbf7d0;
  }

  /* Priority selector styles */
  .priority-selector {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .priority-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    color: #374151;
    text-align: center;
  }

  .priority-btn:hover:not(:disabled):not(.selected) {
    border-color: var(--priority-color);
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .priority-btn.selected {
    border-color: var(--priority-color);
    background: var(--priority-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .priority-btn.selected:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    /* Keep the same background color, just enhance the shadow */
  }

  .priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  /* When selected, make the indicator white to contrast with colored background */
  .priority-btn.selected .priority-indicator {
    background: white;
  }

  /* When not selected, show the priority color */
  .priority-btn:not(.selected) .priority-indicator {
    background: var(--priority-color);
  }

  /* Category dropdown styles */
  .category-dropdown-container {
    position: relative;
  }

  .category-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .category-dropdown-trigger:hover:not(:disabled) {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .category-dropdown-trigger:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  .selected-category {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .dropdown-arrow {
    transition: transform 0.2s ease;
    color: #6b7280;
  }

  .dropdown-arrow.rotated {
    transform: rotate(180deg);
  }

  .category-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    margin-top: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    backdrop-filter: blur(8px);
  }

  .category-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.875rem 1rem;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    font-weight: 500;
    color: #374151;
    border-radius: 0;
  }

  .category-option:first-child {
    border-radius: 12px 12px 0 0;
  }

  .category-option:last-child {
    border-radius: 0 0 12px 12px;
  }

  .category-option:only-child {
    border-radius: 12px;
  }

  .category-option:hover:not(:disabled) {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
  }

  .category-option.selected {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    font-weight: 600;
  }

  .category-option.selected:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
  }

  .category-color {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Scrollbar styling for category dropdown */
  .category-dropdown-menu::-webkit-scrollbar {
    width: 6px;
  }

  .category-dropdown-menu::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }

  .category-dropdown-menu::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
  }

  .category-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Subtasks styles */
  .subtasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .subtask-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .subtask-row input {
    flex: 1;
    padding: 0.75rem;
    border-radius: 10px;
  }

  .subtask-textarea {
    flex: 1;
    padding: 0.75rem;
    border-radius: 10px;
    border: 1px solid #d1d5db;
    resize: vertical;
    min-height: 48px;
    font-family: inherit;
  }

  .remove-subtask-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e5e7eb;
    background: white;
    color: #6b7280;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .remove-subtask-btn:hover:not(:disabled) {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
    transform: scale(1.05);
  }

  .remove-subtask-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
  }

  .add-subtask-btn {
    padding: 0.75rem 1.25rem;
    border: 2px dashed #d1d5db;
    background: transparent;
    color: #6b7280;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .add-subtask-btn:hover:not(:disabled) {
    border-color: #4299e1;
    color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
  }
</style>
