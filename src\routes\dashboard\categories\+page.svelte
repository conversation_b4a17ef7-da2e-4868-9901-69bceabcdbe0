<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import { slide, fade } from 'svelte/transition';

  export let data: PageData;

  let categories = data.categories;
  let showAddForm = false;
  let newCategoryName = '';
  let newCategoryColor = '#3b82f6';
  let loading = false;
  let error = '';
  let success = '';
  
  // Delete confirmation modal state
  let showDeleteModal = false;
  let categoryToDelete: { id: string; name: string } | null = null;

  const predefinedColors = [
    '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444',
    '#06b6d4', '#ec4899', '#84cc16', '#f97316', '#6366f1'
  ];

  async function addCategory() {
    if (!newCategoryName.trim()) {
      error = 'Category name is required';
      return;
    }

    loading = true;
    error = '';
    success = '';

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCategoryName.trim(),
          color: newCategoryColor
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.category) {
          categories = [...categories, data.category];
          newCategoryName = '';
          newCategoryColor = '#3b82f6';
          showAddForm = false;
          success = 'Category added successfully!';
          setTimeout(() => success = '', 3000);
        } else {
          error = 'Invalid response from server';
        }
      } else {
        error = data.error || 'Failed to add category';
      }
    } catch (err) {
      console.error('Error adding category:', err);
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  function openDeleteModal(category: { id: string; name: string }) {
    categoryToDelete = category;
    showDeleteModal = true;
  }

  function closeDeleteModal() {
    showDeleteModal = false;
    categoryToDelete = null;
  }

  async function confirmDelete() {
    if (!categoryToDelete) return;

    loading = true;
    error = '';
    const categoryId = categoryToDelete.id;
    closeDeleteModal();

    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        categories = categories.filter(c => c.id !== categoryId);
        success = 'Category deleted successfully!';
        setTimeout(() => success = '', 3000);
      } else {
        const { error: errorMsg } = await response.json();
        error = errorMsg || 'Failed to delete category';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<div class="categories-container">
  <div class="categories-header">
    <h1>Manage Categories</h1>
    <p>Organize your tasks with custom categories</p>
  </div>

  {#if error}
    <div class="error-message">{error}</div>
  {/if}

  {#if success}
    <div class="success-message">{success}</div>
  {/if}

  <div class="categories-content">
    <div class="add-category-section">
      <button
        type="button"
        class="add-category-btn"
        on:click={() => showAddForm = !showAddForm}
        disabled={loading}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
        Add New Category
      </button>

      {#if showAddForm}
        <div class="add-form" transition:slide>
          <div class="form-group">
            <label for="categoryName">Category Name</label>
            <input
              id="categoryName"
              type="text"
              bind:value={newCategoryName}
              placeholder="Enter category name"
              disabled={loading}
              maxlength="50"
              on:keydown={(e) => {
                if (e.key === 'Enter' && !loading && newCategoryName.trim()) {
                  addCategory();
                }
              }}
            />
          </div>

          <div class="form-group">
            <label for="categoryColor">Color</label>
            <div class="color-picker">
              {#each predefinedColors as color}
                <button
                  type="button"
                  class="color-option"
                  class:selected={newCategoryColor === color}
                  style="background: {color};"
                  on:click={() => newCategoryColor = color}
                  disabled={loading}
                ></button>
              {/each}
            </div>
          </div>

          <div class="form-actions">
            <button
              type="button"
              class="btn-secondary"
              on:click={() => { showAddForm = false; newCategoryName = ''; newCategoryColor = '#3b82f6'; }}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn-primary"
              on:click={addCategory}
              disabled={loading || !newCategoryName.trim()}
            >
              {loading ? 'Adding...' : 'Add Category'}
            </button>
          </div>
        </div>
      {/if}
    </div>

    <div class="categories-list">
      {#if categories.length === 0}
        <div class="empty-state">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
          <h3>No categories yet</h3>
          <p>Create your first category to organize your tasks</p>
        </div>
      {:else}
        <div class="categories-grid">
          {#each categories as category}
            <div class="category-card">
              <div class="category-info">
                <div class="category-color" style="background: {category.color};"></div>
                <span class="category-name">{category.name}</span>
              </div>
              <button
                type="button"
                class="delete-btn"
                on:click={() => openDeleteModal(category)}
                disabled={loading}
                title="Delete category"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"></polyline>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                </svg>
              </button>
            </div>
            
            <!-- Delete Confirmation Modal -->
            {#if showDeleteModal}
              <div class="modal-overlay" transition:fade={{ duration: 200 }} on:click={closeDeleteModal}>
                <div class="modal-content" on:click|stopPropagation transition:slide={{ duration: 200 }}>
                  <div class="modal-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                  </div>
                  
                  <h3 class="modal-title">Delete Category</h3>
                  
                  <p class="modal-message">
                    Are you sure you want to delete <strong>"{categoryToDelete?.name}"</strong>?
                    This action cannot be undone.
                  </p>
                  
                  <div class="modal-actions">
                    <button
                      type="button"
                      class="modal-btn-cancel"
                      on:click={closeDeleteModal}
                      disabled={loading}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      class="modal-btn-delete"
                      on:click={confirmDelete}
                      disabled={loading}
                    >
                      {loading ? 'Deleting...' : 'Delete Category'}
                    </button>
                  </div>
                </div>
              </div>
            {/if}
          {/each}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .categories-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .categories-header {
    margin-bottom: 2rem;
  }

  .categories-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }

  .categories-header p {
    color: #6b7280;
    font-size: 1rem;
  }

  .error-message, .success-message {
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
  }

  .error-message {
    background: linear-gradient(135deg, #fef2f2, #fed7d7);
    color: #dc2626;
    border: 1px solid #fecaca;
  }

  .success-message {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #16a34a;
    border: 1px solid #bbf7d0;
  }

  .categories-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .add-category-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .add-category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    margin: 0 auto;
    justify-content: center;
  }

  .add-category-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  }

  .add-form {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-group input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  .color-picker {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .color-option:hover {
    transform: scale(1.1);
  }

  .color-option.selected {
    border-color: #1f2937;
    transform: scale(1.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  }

  .btn-secondary {
    background: white;
    color: #374151;
    border-color: #e5e7eb;
  }

  .btn-secondary:hover:not(:disabled) {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .categories-list {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
  }

  .empty-state svg {
    margin: 0 auto 1rem;
    opacity: 0.5;
  }

  .empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  .category-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: #f8fafc;
    transition: all 0.2s ease;
  }

  .category-card:hover {
    border-color: #d1d5db;
    background: #f1f5f9;
  }

  .category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .category-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .category-name {
    font-weight: 600;
    color: #374151;
  }

  .delete-btn {
    padding: 0.5rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .delete-btn:hover:not(:disabled) {
    color: #ef4444;
    background: #fef2f2;
  }

  @media (max-width: 768px) {
    .categories-container {
      padding: 1rem;
    }

    .categories-grid {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column;
    }

    .add-category-section {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .add-category-btn {
      width: auto;
      margin: 0 auto;
    }
  }

  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modal-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    max-width: 400px;
    width: 100%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .modal-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: #dc2626;
  }

  .modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
  }

  .modal-message {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 2rem;
  }

  .modal-message strong {
    color: #374151;
    font-weight: 600;
  }

  .modal-actions {
    display: flex;
    gap: 1rem;
    width: 100%;
  }

  .modal-btn-cancel,
  .modal-btn-delete {
    flex: 1;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    font-size: 0.875rem;
  }

  .modal-btn-cancel {
    background: #f3f4f6;
    color: #374151;
  }

  .modal-btn-cancel:hover:not(:disabled) {
    background: #e5e7eb;
    transform: translateY(-1px);
  }

  .modal-btn-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }

  .modal-btn-delete:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  }

  .modal-btn-cancel:disabled,
  .modal-btn-delete:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 768px) {
    .modal-content {
      padding: 1.5rem;
      max-width: 90%;
    }

    .modal-icon {
      width: 48px;
      height: 48px;
      margin-bottom: 1rem;
    }

    .modal-icon svg {
      width: 32px;
      height: 32px;
    }

    .modal-title {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }

    .modal-message {
      font-size: 0.875rem;
      margin-bottom: 1.5rem;
    }

    .modal-actions {
      flex-direction: column-reverse;
      gap: 0.75rem;
    }

    .modal-btn-cancel,
    .modal-btn-delete {
      width: 100%;
      padding: 0.75rem 1.25rem;
    }
  }
</style>
