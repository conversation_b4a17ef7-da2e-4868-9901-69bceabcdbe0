<script lang="ts">
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import type { PageData } from './$types';
  import { fade, crossfade } from 'svelte/transition';
  import { cubicOut } from 'svelte/easing';
  import Button from '$lib/components/ui/Button.svelte';
  import Badge from '$lib/components/ui/Badge.svelte';
  import TaskCard from '$lib/components/TaskCard.svelte';

  export let data: PageData;

  let filter = 'all'; // 'all', 'active', 'completed', 'today', 'overdue'
  let searchQuery = '';
  let showDeleteModal = false;
  let taskToDelete: string | null = null;
  let deletingTask = false;

  // Create crossfade for smooth transitions between incomplete and completed lists
  const [send, receive] = crossfade({
    duration: 300,
    easing: cubicOut
  });

  // Split tasks into incomplete and completed based on filter
  $: incompleteTasks = data.tasks.all.filter(task => {
    // Apply search filter first
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!task.title.toLowerCase().includes(query) && 
          !task.subtitle?.toLowerCase().includes(query) &&
          !task.notes?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply status filter for incomplete tasks
    switch (filter) {
      case 'active':
        return !task.completed;
      case 'completed':
        return false; // Show no incomplete tasks when filter is completed
      case 'today':
        return data.tasks.today.includes(task) && !task.completed;
      case 'overdue':
        return data.tasks.overdue.includes(task) && !task.completed;
      default:
        return !task.completed;
    }
  });

  $: completedTasks = data.tasks.all.filter(task => {
    // Apply search filter first
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!task.title.toLowerCase().includes(query) && 
          !task.subtitle?.toLowerCase().includes(query) &&
          !task.notes?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply status filter for completed tasks
    switch (filter) {
      case 'active':
        return false; // Show no completed tasks when filter is active
      case 'completed':
        return task.completed;
      case 'today':
        return data.tasks.today.includes(task) && task.completed;
      case 'overdue':
        return data.tasks.overdue.includes(task) && task.completed;
      default:
        return task.completed;
    }
  });

  function formatDate(dateString: string | null): string {
    if (!dateString) return 'No due date';
    
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 3: return 'High';
      case 2: return 'Normal';
      case 1: return 'Low';
      default: return 'Normal';
    }
  }

  function getPriorityVariant(priority: number): 'danger' | 'warning' | 'primary' {
    switch (priority) {
      case 3: return 'danger';
      case 2: return 'warning';
      case 1: return 'primary';
      default: return 'warning';
    }
  }

  function getCategoryName(categoryId: string | null): string {
    if (!categoryId) return '';
    const category = data.categories.find(c => c.id === categoryId);
    return category?.name || '';
  }

  async function toggleTask(taskId: string, completed: boolean) {
    try {
      if (!completed) {
        // Complete the task
        const response = await fetch(`/api/tasks/${taskId}/complete`, {
          method: 'POST'
        });
        
        if (response.ok) {
          await invalidateAll();
        }
      } else {
        // Uncomplete the task
        const response = await fetch(`/api/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ completed: false, completedAt: null })
        });

        if (response.ok) {
          await invalidateAll();
        }
      }
    } catch (error) {
      console.error('Error toggling task:', error);
    }
  }

  function confirmDeleteTask(taskId: string) {
    taskToDelete = taskId;
    showDeleteModal = true;
  }

  function cancelDelete() {
    showDeleteModal = false;
    taskToDelete = null;
  }

  async function deleteTask() {
    if (!taskToDelete) return;

    deletingTask = true;
    try {
      const response = await fetch(`/api/tasks/${taskToDelete}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await invalidateAll();
        showDeleteModal = false;
        taskToDelete = null;
      } else {
        console.error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    } finally {
      deletingTask = false;
    }
  }
</script>

<svelte:head>
  <title>Tasks - Routine Mail</title>
</svelte:head>

<!-- Desktop Header -->
<div class="mb-8 hidden md:block">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 class="text-2xl font-bold text-secondary-900">Tasks</h1>
      <p class="text-secondary-600 mt-1">Manage your tasks and routines</p>
    </div>
    <Button href="/dashboard/tasks/new" variant="primary">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
      </svg>
      New Task
    </Button>
  </div>
</div>

<!-- Mobile: No header, more space for tasks -->

<!-- Desktop Search and Filters -->
<div class="card mb-6 hidden md:block">
  <div class="card-body space-y-4">
    <!-- Search Box -->
    <div>
      <input
        type="text"
        placeholder="Search tasks..."
        bind:value={searchQuery}
        class="form-input"
      />
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
      <button
        class="filter-tab"
        class:active={filter === 'all'}
        on:click={() => filter = 'all'}
      >
        All ({data.tasks.all.length})
      </button>
      <button
        class="filter-tab"
        class:active={filter === 'active'}
        on:click={() => filter = 'active'}
      >
        Active ({data.tasks.active.length})
      </button>
      <button
        class="filter-tab"
        class:active={filter === 'today'}
        on:click={() => filter = 'today'}
      >
        Today ({data.tasks.today.length})
      </button>
      <button
        class="filter-tab"
        class:active={filter === 'overdue'}
        on:click={() => filter = 'overdue'}
      >
        Overdue ({data.tasks.overdue.length})
      </button>
      <button
        class="filter-tab"
        class:active={filter === 'completed'}
        on:click={() => filter = 'completed'}
      >
        Completed ({data.tasks.completed.length})
      </button>
    </div>
  </div>
</div>

<!-- Mobile Compact Search and Filters -->
<div class="md:hidden mb-3">
  <!-- Compact Search Bar -->
  <div class="px-4 mb-2">
    <div class="relative">
      <input
        type="text"
        placeholder="Search tasks..."
        bind:value={searchQuery}
        class="w-full pl-10 pr-4 py-2.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
      />
      <svg class="absolute left-3 top-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </div>
  </div>

  <!-- Compact Filter Tabs -->
  <div class="px-4">
    <div class="flex gap-1 overflow-x-auto pb-2">
      <button
        class="compact-filter-tab"
        class:active={filter === 'all'}
        on:click={() => filter = 'all'}
      >
        All ({data.tasks.all.length})
      </button>
      <button
        class="compact-filter-tab"
        class:active={filter === 'active'}
        on:click={() => filter = 'active'}
      >
        Active ({data.tasks.active.length})
      </button>
      <button
        class="compact-filter-tab"
        class:active={filter === 'today'}
        on:click={() => filter = 'today'}
      >
        Today ({data.tasks.today.length})
      </button>
      <button
        class="compact-filter-tab"
        class:active={filter === 'overdue'}
        on:click={() => filter = 'overdue'}
      >
        Overdue ({data.tasks.overdue.length})
      </button>
      <button
        class="compact-filter-tab"
        class:active={filter === 'completed'}
        on:click={() => filter = 'completed'}
      >
        Done ({data.tasks.completed.length})
      </button>
    </div>
  </div>
</div>

{#if incompleteTasks.length === 0 && completedTasks.length === 0}
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-secondary-900">
      {#if searchQuery}
        No tasks found
      {:else}
        No tasks yet
      {/if}
    </h3>
    <p class="mt-1 text-sm text-secondary-500">
      {#if searchQuery}
        No tasks found matching "{searchQuery}"
      {:else}
        Get started by creating your first task.
      {/if}
    </p>
    {#if !searchQuery}
      <div class="mt-6">
        <Button href="/dashboard/tasks/new" variant="primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Create a Task
        </Button>
      </div>
    {/if}
  </div>
{:else}
  <!-- Mobile View: Split Layout with Task Cards -->
  <div class="block md:hidden px-4">
    <!-- Incomplete Tasks -->
    {#if incompleteTasks.length > 0}
      <div class="space-y-2 mb-6">
        {#each incompleteTasks as task (task.id)}
          <div key={task.id} in:receive|local={{key: task.id}} out:send|local={{key: task.id}}>
            <TaskCard
              {task}
              categories={data.categories}
              compact={true}
              on:toggle={(e) => toggleTask(e.detail, task.completed)}
              on:edit={(e) => goto(`/dashboard/tasks/${e.detail}/edit`)}
              on:delete={(e) => confirmDeleteTask(e.detail)}
            />
          </div>
        {/each}
      </div>
    {/if}

    <!-- Completed Tasks Section -->
    {#if completedTasks.length > 0}
      <div class="completed-section">
        <details open>
          <summary>
            Completed ({completedTasks.length})
            <div class="divider"></div>
          </summary>
          <div class="space-y-2 mt-3">
            {#each completedTasks as task (task.id)}
              <div key={task.id} in:receive|local={{key: task.id}} out:send|local={{key: task.id}}>
                <TaskCard
                  {task}
                  categories={data.categories}
                  compact={true}
                  on:toggle={(e) => toggleTask(e.detail, task.completed)}
                  on:edit={(e) => goto(`/dashboard/tasks/${e.detail}/edit`)}
                  on:delete={(e) => confirmDeleteTask(e.detail)}
                />
              </div>
            {/each}
          </div>
        </details>
      </div>
    {/if}
  </div>

  <!-- Desktop View: Split Tables -->
  <div class="hidden md:block">
    <!-- Incomplete Tasks Table -->
    {#if incompleteTasks.length > 0}
      <div class="table-container mb-6">
        <table class="table">
          <thead class="table-header">
            <tr>
              <th class="w-12"></th>
              <th>Task</th>
              <th>Priority</th>
              <th>Due Date</th>
              <th>Category</th>
              <th class="w-24">Actions</th>
            </tr>
          </thead>
          <tbody class="table-body">
            {#each incompleteTasks as task (task.id)}
              <tr key={task.id} class="table-row" in:receive|local={{key: task.id}} out:send|local={{key: task.id}}>
                <td class="table-cell">
                  <input
                    type="checkbox"
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                    checked={task.completed}
                    on:change|stopPropagation={() => toggleTask(task.id, task.completed)}
                  />
                </td>
                <td class="table-cell">
                  <a href="/dashboard/tasks/{task.id}" class="block hover:text-primary-600 transition-colors" on:click|stopPropagation>
                    <div class="font-medium text-secondary-900">
                      {task.title}
                    </div>
                    {#if task.subtitle}
                      <div class="text-sm text-secondary-500 mt-1">{task.subtitle}</div>
                    {/if}
                  </a>
                </td>
                <td class="table-cell">
                  <Badge variant={getPriorityVariant(task.priority)} size="sm">
                    {getPriorityLabel(task.priority)}
                  </Badge>
                </td>
                <td class="table-cell text-secondary-600">
                  {formatDate(task.dueDate)}
                </td>
                <td class="table-cell">
                  {#if getCategoryName(task.categoryId)}
                    <Badge variant="secondary" size="sm">
                      {getCategoryName(task.categoryId)}
                    </Badge>
                  {/if}
                </td>
                <td class="table-cell">
                  <div class="flex gap-1">
                    <button
                      class="action-btn"
                      on:click|stopPropagation={() => goto(`/dashboard/tasks/${task.id}/edit`)}
                      title="Edit Task"
                      aria-label="Edit task: {task.title}"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </button>
                    <button
                      class="action-btn-danger"
                      on:click|stopPropagation={() => confirmDeleteTask(task.id)}
                      title="Delete Task"
                      aria-label="Delete task: {task.title}"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                  </div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}

    <!-- Completed Tasks Table -->
    {#if completedTasks.length > 0}
      <div class="completed-section">
        <details open>
          <summary>
            Completed ({completedTasks.length})
            <div class="divider"></div>
          </summary>
          <div class="table-container mt-3">
            <table class="table">
              <thead class="table-header">
                <tr>
                  <th class="w-12"></th>
                  <th>Task</th>
                  <th>Priority</th>
                  <th>Due Date</th>
                  <th>Category</th>
                  <th class="w-24">Actions</th>
                </tr>
              </thead>
              <tbody class="table-body">
                {#each completedTasks as task (task.id)}
                  <tr key={task.id} class="table-row opacity-70" in:receive|local={{key: task.id}} out:send|local={{key: task.id}}>
                    <td class="table-cell">
                      <input
                        type="checkbox"
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                        checked={task.completed}
                        on:change|stopPropagation={() => toggleTask(task.id, task.completed)}
                      />
                    </td>
                    <td class="table-cell">
                      <a href="/dashboard/tasks/{task.id}" class="block hover:text-primary-600 transition-colors" on:click|stopPropagation>
                        <div class="font-medium text-secondary-900 line-through">
                          {task.title}
                        </div>
                        {#if task.subtitle}
                          <div class="text-sm text-secondary-500 mt-1 line-through">{task.subtitle}</div>
                        {/if}
                      </a>
                    </td>
                    <td class="table-cell">
                      <Badge variant={getPriorityVariant(task.priority)} size="sm">
                        {getPriorityLabel(task.priority)}
                      </Badge>
                    </td>
                    <td class="table-cell text-secondary-600">
                      {formatDate(task.dueDate)}
                    </td>
                    <td class="table-cell">
                      {#if getCategoryName(task.categoryId)}
                        <Badge variant="secondary" size="sm">
                          {getCategoryName(task.categoryId)}
                        </Badge>
                      {/if}
                    </td>
                    <td class="table-cell">
                      <div class="flex gap-1">
                        <button
                          class="action-btn"
                          on:click|stopPropagation={() => goto(`/dashboard/tasks/${task.id}/edit`)}
                          title="Edit Task"
                          aria-label="Edit task: {task.title}"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </button>
                        <button
                          class="action-btn-danger"
                          on:click|stopPropagation={() => confirmDeleteTask(task.id)}
                          title="Delete Task"
                          aria-label="Delete task: {task.title}"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </details>
      </div>
    {/if}
  </div>
{/if}

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    on:click={cancelDelete}
    on:keydown={(e) => e.key === 'Escape' && cancelDelete()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="delete-modal-title"
    tabindex="0"
  >
    <div
      class="bg-white rounded-xl shadow-large max-w-md w-full"
      on:click|stopPropagation
      on:keydown|stopPropagation
      role="document"
    >
      <div class="px-6 py-4 border-b border-secondary-200">
        <div class="flex items-center justify-between">
          <h3 id="delete-modal-title" class="text-lg font-semibold text-secondary-900">Delete Task</h3>
          <button
            class="text-secondary-400 hover:text-secondary-600 transition-colors"
            on:click={cancelDelete}
            aria-label="Close delete confirmation dialog"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      <div class="px-6 py-4">
        <p class="text-secondary-600">Are you sure you want to delete this task? This action cannot be undone.</p>
      </div>
      <div class="px-6 py-4 border-t border-secondary-200 flex gap-3 justify-end">
        <Button variant="secondary" on:click={cancelDelete} disabled={deletingTask}>
          Cancel
        </Button>
        <Button variant="danger" on:click={deleteTask} disabled={deletingTask} loading={deletingTask}>
          {#if deletingTask}
            Deleting...
          {:else}
            Delete Task
          {/if}
        </Button>
      </div>
    </div>
  </div>
{/if}

<!-- Mobile Floating Action Button -->
<div class="md:hidden">
  <a
    href="/dashboard/tasks/new"
    class="fixed bottom-20 right-4 w-12 h-12 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-40"
    aria-label="Create new task"
  >
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
    </svg>
  </a>
</div>

<style>
  .compact-filter-tab {
    @apply px-3 py-1.5 text-xs font-medium rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex-shrink-0;
  }

  .compact-filter-tab.active {
    @apply bg-primary-600 text-white border-primary-600 hover:bg-primary-700;
  }

  /* Completed Section Styling */
  .completed-section {
    margin-top: 1.5rem;
  }

  .completed-section summary {
    font-weight: 600;
    font-size: 1rem;
    color: #343a40;
    cursor: pointer;
    padding: 0.75rem 1.25rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .completed-section summary::marker {
    content: '';
  }

  .completed-section summary:before {
    content: '▶';
    font-size: 0.8em;
    transition: transform 0.2s;
  }

  .completed-section details[open] > summary:before {
    transform: rotate(90deg);
  }

  .divider {
    flex: 1;
    height: 1px;
    background-color: #dee2e6;
  }

  /* Mobile completed section adjustments */
  @media (max-width: 768px) {
    .completed-section summary {
      padding: 0.75rem 0;
      background-color: transparent;
      border: none;
      border-top: 1px solid #dee2e6;
    }
  }
</style>




