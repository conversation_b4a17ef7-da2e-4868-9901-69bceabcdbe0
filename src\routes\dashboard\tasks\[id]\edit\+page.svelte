<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import TaskForm from '$lib/components/TaskForm.svelte';

  export let data: PageData;

  const { task, categories } = data;

  // Initialize form fields with existing task data
  let title = task.title;
  let notes = task.notes || '';
  let priority = task.priority;
  let categoryId = task.categoryId || '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = !!task.dueDate;
  let subtasks: string[] = task.subtasks || [''];
  let loading = false;
  let error = '';
  let success = '';
  let showCategoryDropdown = false;

  // New recurrence rule state, initialized with task data
  let recurrenceRule: any = task.recurrenceRule || null;
  let hasRecurrence = !!task.recurrenceRule;

  // Initialize date and time from existing task
  if (task.dueDate) {
    const taskDate = new Date(task.dueDate);
    dueDate = taskDate.toISOString().split('T')[0];
    dueTime = taskDate.toTimeString().slice(0, 5);
  }

  // Ensure subtasks array has at least one empty string for the UI
  if (subtasks.length === 0) {
    subtasks = [''];
  }

  async function handleSubmit(event: CustomEvent) {
    const formData = event.detail;
    
    loading = true;
    error = '';

    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        // Show brief success message then redirect
        success = 'Task updated successfully!';
        // Small delay to show success message, then redirect
        setTimeout(() => {
          goto(`/dashboard/tasks/${task.id}`, { invalidateAll: true });
        }, 500);
      } else {
        error = result.error || 'Failed to update task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  function handleCancel() {
    goto(`/dashboard/tasks/${task.id}`);
  }
</script>

<svelte:head>
  <title>Edit {task.title} - Routine Mail</title>
</svelte:head>

<div class="page-container">
  <!-- Desktop Header -->
  <div class="page-header hidden md:block">
    <h1 class="page-title">Edit Task</h1>
    <p class="page-subtitle">Update your task details</p>
  </div>

  <TaskForm
    mode="edit"
    bind:title
    bind:notes
    bind:priority
    bind:categoryId
    bind:dueDate
    bind:dueTime
    bind:hasDueDate
    bind:subtasks
    bind:loading
    bind:error
    bind:success
    bind:showCategoryDropdown
    bind:hasRecurrence
    bind:recurrenceRule
    categories={categories}
    on:submit={handleSubmit}
    on:cancel={handleCancel}
  />
</div>

<style>
  .page-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 1rem;
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
    letter-spacing: -0.02em;
  }

  .page-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
  }

  /* Mobile-first improvements */
  @media (max-width: 768px) {
    .page-header {
      margin-bottom: 0.75rem;
    }
  }
</style>
